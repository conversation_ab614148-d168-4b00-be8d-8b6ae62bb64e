import React from "react";
import { useSelector } from "react-redux";
import { useRoute } from "@react-navigation/native";
import {
  getDepartmentsByCompany,
  getPeopleByCompany,
  getVesselsByCompany,
} from "../components/contacts/helpers/contactFilters";

// Components
import GeneralContactScreen from "../components/contacts/GeneralContactScreen";

const ContactDetailsHOC: React.FC = () => {
  const contacts = useSelector(
    (state: any) => state.persist?.bTeamContactsSlice.contacts
  );
  const route = useRoute();
  const contactGuid = route.params?.contactGuid;

  const departmentsList = getDepartmentsByCompany(contacts, companyGuid).map(
    (id: string) => contacts.byId[id]
  );
  const peopleList = getPeopleByCompany(contacts, companyGuid).map(
    (id: string) => contacts.byId[id]
  );
  const vesselsList = getVesselsByCompany(contacts, companyGuid).map(
    (id: string) => contacts.byId[id]
  );

  return (
    <GeneralContactScreen
      departmentsList={departmentsList}
      peopleList={peopleList}
      vesselsList={vesselsList}
      companyGuid={contactGuid}
    />
  );
};

export default ContactDetailsHOC;
