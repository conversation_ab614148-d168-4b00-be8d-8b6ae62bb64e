import React from "react";
import { useNavigation } from "@react-navigation/native";
import { FlatList, Pressable, StyleSheet, Text, View } from "react-native";

import { type Theme, useThemeAwareObject } from "b-ui-lib";
import { Contact } from "../../types/contact";
import { SCREEN_NAMES } from "../../constants/screenNames";
import ContactItem from "../contactList/ContactItem";

type Props = {
  data: Contact[];
};

const EmptyList = () => {
  return (
    <View>
      <Text>No data</Text>
    </View>
  );
};

const List = ({ data }: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);
  const navigation = useNavigation();
  const contactType = data.map((contact: Contact) => contact.CNT_Type)?.[0];

  const navigateToScreen = (contactGuid: string) => {
    const navDictionary = {
      2: SCREEN_NAMES.contactDetails,
      4: SCREEN_NAMES.vesselDetails,
      8: SCREEN_NAMES.departmentDetails,
    };

    navigation.navigate(navDictionary[contactType], {
      contactGuid,
    });
  };

  return (
    <FlatList
      data={data}
      keyExtractor={(item) => item.CNT_Guid}
      renderItem={({ item }) => (
        <ContactItem
          contact={item}
          onPress={() => navigateToScreen(item.CNT_Guid)}
        />
      )}
      ListEmptyComponent={EmptyList}
    />
  );
};

export default List;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({});

  return { styles, color };
};
